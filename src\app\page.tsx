"use client"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { account } from "./appwrite"

export default function Page() {
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        await account.get()
        router.push('/dashboard/overview')
      } catch {
        router.push('/login')
      }
    }
    checkAuth()
  }, [router])

  return null // No need for UI as we're just handling routing
}

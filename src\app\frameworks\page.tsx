import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, FileCheck, Building, CreditCard, Heart, Layers, Network } from "lucide-react"

export default function FrameworksPage() {
  const frameworks = [
    {
      title: "NIST CSF 2.0",
      description: "NIST Cybersecurity Framework implementation",
      icon: Shield,
      href: "/frameworks/nist-csf",
      stats: "87% Complete",
    },
    {
      title: "ISO 27001",
      description: "Information Security Management System",
      icon: FileCheck,
      href: "/frameworks/iso-27001",
      stats: "92% Complete",
    },
    {
      title: "SOC 2",
      description: "Service Organization Control 2 compliance",
      icon: Building,
      href: "/frameworks/soc-2",
      stats: "78% Complete",
    },
    {
      title: "PCI DSS",
      description: "Payment Card Industry Data Security Standard",
      icon: CreditCard,
      href: "/frameworks/pci-dss",
      stats: "95% Complete",
    },
    {
      title: "HIPAA",
      description: "Health Insurance Portability and Accountability Act",
      icon: Heart,
      href: "/frameworks/hipaa",
      stats: "83% Complete",
    },
    {
      title: "OSCAL Modeling",
      description: "OSCAL-based framework modeling and management",
      icon: Layers,
      href: "/frameworks/oscal",
      stats: "Active",
    },
    {
      title: "Multi-Framework",
      description: "Multi-framework harmonization and control mapping",
      icon: Network,
      href: "/frameworks/harmonization",
      stats: "Harmonized",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Frameworks</h1>
        <p className="text-muted-foreground">
          Compliance framework library with OSCAL-based modeling and Compliance Agent automated analysis
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {frameworks.map((framework) => (
          <Card key={framework.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <framework.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{framework.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {framework.stats}
                </div>
              </div>
              <CardDescription>{framework.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to manage {framework.title} framework
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

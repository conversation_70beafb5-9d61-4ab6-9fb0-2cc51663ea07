import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { FileText, Code, Bot, CheckCircle, Shield } from "lucide-react"

export default function PoliciesPage() {
  const policySections = [
    {
      title: "Management",
      description: "Policy management and enforcement across all environments",
      icon: FileText,
      href: "/policies/management",
      stats: "156 Policies",
    },
    {
      title: "Policy as Code",
      description: "OPA-based policy-as-code implementation",
      icon: Code,
      href: "/policies/code",
      stats: "OPA Active",
    },
    {
      title: "Translation",
      description: "Compliance Agent policy translation and application",
      icon: Bot,
      href: "/policies/translation",
      stats: "Automated",
    },
    {
      title: "Consistency",
      description: "Cross-environment policy consistency verification",
      icon: CheckCircle,
      href: "/policies/consistency",
      stats: "98% Consistent",
    },
    {
      title: "Enforcement",
      description: "OPA policy enforcement integration",
      icon: Shield,
      href: "/policies/enforcement",
      stats: "Real-time",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Policies</h1>
        <p className="text-muted-foreground">
          Policy management and enforcement across all environments with OPA-based policy-as-code implementation
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {policySections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access policy {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

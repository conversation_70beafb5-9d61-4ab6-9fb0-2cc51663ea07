import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { FileText, BarChart3, Users, Clock, Search } from "lucide-react"

export default function ReportsPage() {
  const reportSections = [
    {
      title: "Compliance Documentation",
      description: "Reporting Agent generated compliance documentation",
      icon: FileText,
      href: "/reports/compliance",
      stats: "156 Reports",
    },
    {
      title: "Interactive Dashboards",
      description: "Interactive dashboards and static report artifacts",
      icon: BarChart3,
      href: "/reports/dashboards",
      stats: "12 Dashboards",
    },
    {
      title: "Executive Summaries",
      description: "Executive summaries and regulatory submission preparation",
      icon: Users,
      href: "/reports/executive",
      stats: "C-Suite Ready",
    },
    {
      title: "Real-time Status",
      description: "Real-time compliance status and trend analysis",
      icon: Clock,
      href: "/reports/realtime",
      stats: "Live Updates",
    },
    {
      title: "Audit Reports",
      description: "Audit preparation and reports",
      icon: Search,
      href: "/reports/audit",
      stats: "Audit-Ready",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
        <p className="text-muted-foreground">
          Reporting Agent generated compliance documentation with interactive dashboards and executive summaries
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {reportSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

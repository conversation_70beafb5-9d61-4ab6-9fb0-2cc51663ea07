import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Shield<PERSON>heck, Play, CheckCircle, BarChart3, Network } from "lucide-react"

export default function ControlsPage() {
  const controlSections = [
    {
      title: "Implementation",
      description: "Security control implementation and tracking",
      icon: Shield<PERSON>he<PERSON>,
      href: "/controls/implementation",
      stats: "234 Controls",
    },
    {
      title: "Testing",
      description: "OSCAL standardized control testing",
      icon: Play,
      href: "/controls/testing",
      stats: "89% Tested",
    },
    {
      title: "Assessment",
      description: "Compliance Agent automated control assessment",
      icon: CheckCircle,
      href: "/controls/assessment",
      stats: "Automated",
    },
    {
      title: "Validation",
      description: "Control validation and effectiveness measurement",
      icon: BarChart3,
      href: "/controls/validation",
      stats: "92% Effective",
    },
    {
      title: "Mapping",
      description: "Cross-framework control mapping",
      icon: Network,
      href: "/controls/mapping",
      stats: "Multi-Framework",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Controls</h1>
        <p className="text-muted-foreground">
          Security control implementation and testing with OSCAL standardization and OPA policy enforcement
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {controlSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access control {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

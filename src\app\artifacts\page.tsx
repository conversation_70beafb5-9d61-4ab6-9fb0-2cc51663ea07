import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Archive, Database, Shield, Search, FolderArchive } from "lucide-react"

export default function ArtifactsPage() {
  const artifactSections = [
    {
      title: "Evidence Repository",
      description: "Blockchain-secured evidence repository",
      icon: Archive,
      href: "/artifacts/evidence",
      stats: "2,456 Items",
    },
    {
      title: "Documentation Storage",
      description: "Immutable compliance documentation storage",
      icon: Database,
      href: "/artifacts/documentation",
      stats: "Immutable",
    },
    {
      title: "Verification",
      description: "Cryptographic verification of all security artifacts",
      icon: Shield,
      href: "/artifacts/verification",
      stats: "100% Verified",
    },
    {
      title: "Audit-Ready Evidence",
      description: "Audit-ready evidence with tamper-proof integrity",
      icon: Search,
      href: "/artifacts/audit",
      stats: "Tamper-Proof",
    },
    {
      title: "Archive Management",
      description: "Evidence archival and retention management",
      icon: FolderArchive,
      href: "/artifacts/archive",
      stats: "7-Year Retention",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Artifacts</h1>
        <p className="text-muted-foreground">
          Blockchain-secured evidence repository with immutable compliance documentation and cryptographic verification
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {artifactSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Bar<PERSON>hart3, Bot, Search, Calculator, Clock } from "lucide-react"

export default function AssessmentsPage() {
  const assessmentSections = [
    {
      title: "Risk Assessments",
      description: "Automated risk assessments and gap analysis",
      icon: BarChart3,
      href: "/assessments/risk",
      stats: "23 Active",
    },
    {
      title: "Control Testing",
      description: "Assessment Agent orchestrated control testing",
      icon: Bot,
      href: "/assessments/testing",
      stats: "Automated",
    },
    {
      title: "Gap Analysis",
      description: "Compliance gap identification and analysis",
      icon: Search,
      href: "/assessments/gaps",
      stats: "12 Gaps Found",
    },
    {
      title: "Quantitative Risk",
      description: "Quantitative risk analysis with Open Source Risk Engine",
      icon: Calculator,
      href: "/assessments/quantitative",
      stats: "OSRE Active",
    },
    {
      title: "Continuous Assessment",
      description: "Continuous assessment scheduling and execution",
      icon: Clock,
      href: "/assessments/continuous",
      stats: "24/7 Running",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Assessments</h1>
        <p className="text-muted-foreground">
          Automated risk assessments and gap analysis with Assessment Agent orchestrated control testing
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {assessmentSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

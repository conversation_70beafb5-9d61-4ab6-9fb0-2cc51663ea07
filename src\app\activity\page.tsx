import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Activity, Bo<PERSON>, Shield, FileCheck } from "lucide-react"

export default function ActivityPage() {
  const activitySections = [
    {
      title: "Event Stream",
      description: "Unified event stream from all system components",
      icon: Activity,
      href: "/activity/events",
      stats: "1,247 Events Today",
    },
    {
      title: "Agent Actions",
      description: "Agent-initiated actions and automated responses",
      icon: Bot,
      href: "/activity/agents",
      stats: "34 Actions",
    },
    {
      title: "Security Events",
      description: "Security events, compliance activities, and system operations",
      icon: Shield,
      href: "/activity/security",
      stats: "12 Security Events",
    },
    {
      title: "Audit Trail",
      description: "Audit trail with blockchain verification links",
      icon: FileCheck,
      href: "/activity/audit",
      stats: "100% Verified",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Activity</h1>
        <p className="text-muted-foreground">
          Unified event stream from all system components with agent-initiated actions and automated responses
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {activitySections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()} monitoring
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON><PERSON>gle, Bot, Search, Network, CheckCircle } from "lucide-react"

export default function RemediationPage() {
  const remediationSections = [
    {
      title: "Incident Response",
      description: "Incident response and security remediation coordination",
      icon: <PERSON><PERSON><PERSON><PERSON>gle,
      href: "/remediation/incidents",
      stats: "3 Active",
    },
    {
      title: "Automated Response",
      description: "Remediation Agent automated response orchestration",
      icon: Bot,
      href: "/remediation/automated",
      stats: "AI-Orchestrated",
    },
    {
      title: "DFIR Integration",
      description: "DFIR-IRIS integration for structured investigation",
      icon: Search,
      href: "/remediation/dfir",
      stats: "IRIS Connected",
    },
    {
      title: "Cross-Environment",
      description: "Cross-environment remediation tracking and validation",
      icon: Network,
      href: "/remediation/cross-env",
      stats: "Multi-Env",
    },
    {
      title: "Validation",
      description: "Remediation validation and testing",
      icon: Check<PERSON>ircle,
      href: "/remediation/validation",
      stats: "95% Success",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Remediation</h1>
        <p className="text-muted-foreground">
          Incident response and security remediation coordination with Remediation Agent automated response orchestration
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {remediationSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

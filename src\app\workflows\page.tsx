import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Workflow, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart3 } from "lucide-react"

export default function WorkflowsPage() {
  const workflowSections = [
    {
      title: "Process Automation",
      description: "Business process automation with Flowable engine",
      icon: Workflow,
      href: "/workflows/automation",
      stats: "45 Workflows",
    },
    {
      title: "Optimization",
      description: "Workflow Agent orchestrated process optimization",
      icon: Bot,
      href: "/workflows/optimization",
      stats: "AI-Optimized",
    },
    {
      title: "Design",
      description: "Custom workflow design and template management",
      icon: Palette,
      href: "/workflows/design",
      stats: "23 Templates",
    },
    {
      title: "Integration",
      description: "Integration with compliance and security processes",
      icon: Link,
      href: "/workflows/integration",
      stats: "Connected",
    },
    {
      title: "Analytics",
      description: "Workflow performance analytics",
      icon: BarChart3,
      href: "/workflows/analytics",
      stats: "Real-time",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Workflows</h1>
        <p className="text-muted-foreground">
          Business process automation with Flowable engine and Workflow Agent orchestrated process optimization
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {workflowSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access workflow {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

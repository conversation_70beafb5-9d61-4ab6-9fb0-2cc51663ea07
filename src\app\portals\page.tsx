import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ist, MessageSquare, Settings } from "lucide-react"

export default function PortalsPage() {
  const portalSections = [
    {
      title: "Trust Portals",
      description: "Stakeholder-specific trust portals and interfaces",
      icon: Users,
      href: "/portals/trust",
      stats: "8 Portals",
    },
    {
      title: "AI Chatbots",
      description: "AI-powered chatbots for audit facilitation",
      icon: Bot,
      href: "/portals/chatbots",
      stats: "AI-Powered",
    },
    {
      title: "Self-Service",
      description: "Self-service compliance questionnaire completion",
      icon: ClipboardList,
      href: "/portals/self-service",
      stats: "24/7 Available",
    },
    {
      title: "Stakeholder Communication",
      description: "Secure stakeholder communication and evidence sharing",
      icon: MessageSquare,
      href: "/portals/communication",
      stats: "Secure",
    },
    {
      title: "Portal Management",
      description: "Portal configuration and access management",
      icon: Setting<PERSON>,
      href: "/portals/management",
      stats: "Centralized",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Portals</h1>
        <p className="text-muted-foreground">
          Stakeholder-specific trust portals with AI-powered chatbots and secure communication interfaces
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {portalSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

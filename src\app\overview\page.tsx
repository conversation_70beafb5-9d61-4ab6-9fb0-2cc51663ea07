import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { LayoutDashboard, Bot, TrendingUp, Shield } from "lucide-react"

export default function OverviewPage() {
  const overviewSections = [
    {
      title: "Executive Summary",
      description: "High-level executive dashboard with System Agent intelligence summaries",
      icon: LayoutDashboard,
      href: "/overview/executive",
      stats: "4 Key Insights",
    },
    {
      title: "System Agent Status",
      description: "Real-time agent activity status and orchestration health",
      icon: Bot,
      href: "/overview/agents",
      stats: "12 Agents Active",
    },
    {
      title: "Risk Posture",
      description: "Cross-domain risk posture and compliance status",
      icon: TrendingUp,
      href: "/overview/risk",
      stats: "Medium Risk",
    },
    {
      title: "Compliance Status",
      description: "Overall compliance status across all frameworks",
      icon: Shield,
      href: "/overview/compliance",
      stats: "87% Compliant",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Overview</h1>
        <p className="text-muted-foreground">
          Executive dashboard with System Agent intelligence summaries and key performance indicators
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {overviewSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access detailed {section.title.toLowerCase()} view
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

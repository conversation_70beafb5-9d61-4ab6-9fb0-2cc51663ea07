import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Server, Cpu, Wifi, Users, Package, Building, GitBranch } from "lucide-react"

export default function AssetsPage() {
  const assetCategories = [
    {
      title: "IT Assets",
      description: "Servers, workstations, network infrastructure",
      icon: Server,
      href: "/assets/it",
      stats: "1,247 Assets",
    },
    {
      title: "OT Assets",
      description: "Industrial control systems, SCADA, manufacturing equipment",
      icon: Cpu,
      href: "/assets/ot",
      stats: "89 Systems",
    },
    {
      title: "IoT Devices",
      description: "Connected sensors, smart devices, edge computing",
      icon: Wifi,
      href: "/assets/iot",
      stats: "3,456 Devices",
    },
    {
      title: "Identities",
      description: "Users, service accounts, privileged access management",
      icon: Users,
      href: "/assets/identities",
      stats: "567 Identities",
    },
    {
      title: "Applications",
      description: "Software inventory, cloud services, SaaS platforms",
      icon: Package,
      href: "/assets/applications",
      stats: "234 Applications",
    },
    {
      title: "Vendors",
      description: "Third-party relationships, supplier risk management",
      icon: Building,
      href: "/assets/vendors",
      stats: "78 Vendors",
    },
    {
      title: "Processes",
      description: "Business workflows, operational procedures, governance processes",
      icon: GitBranch,
      href: "/assets/processes",
      stats: "145 Processes",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Assets</h1>
        <p className="text-muted-foreground">
          Blockchain-secured Configuration Management Database (CMDB) foundation managed by DataGerry + Hyperledger Fabric
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {assetCategories.map((category) => (
          <Card key={category.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <category.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{category.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {category.stats}
                </div>
              </div>
              <CardDescription>{category.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to manage {category.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

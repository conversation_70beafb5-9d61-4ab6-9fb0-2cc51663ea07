import { <PERSON>, CardContent, Card<PERSON>es<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Monitor, Brain, BarChart3, Network, AlertTriangle } from "lucide-react"

export default function MonitorPage() {
  const monitoringSections = [
    {
      title: "Security Dashboard",
      description: "Real-time security monitoring across all asset categories",
      icon: Monitor,
      href: "/monitor/security",
      stats: "Live Monitoring",
    },
    {
      title: "Anomaly Detection",
      description: "AI-powered anomaly detection and behavioral analysis",
      icon: Brain,
      href: "/monitor/anomaly",
      stats: "3 Anomalies",
    },
    {
      title: "SIEM Dashboard",
      description: "Unified SIEM dashboard with Wazuh integration",
      icon: BarChart3,
      href: "/monitor/siem",
      stats: "Wazuh Active",
    },
    {
      title: "Threat Correlation",
      description: "Cross-environment threat correlation (IT/OT/IoT)",
      icon: Network,
      href: "/monitor/correlation",
      stats: "Multi-Environment",
    },
    {
      title: "Alert Prioritization",
      description: "System Agent coordinated alert prioritization",
      icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
      href: "/monitor/alerts",
      stats: "12 High Priority",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Monitor</h1>
        <p className="text-muted-foreground">
          Real-time security monitoring across all asset categories with AI-powered anomaly detection and unified SIEM
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {monitoringSections.map((section) => (
          <Card key={section.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <section.icon className="h-5 w-5" />
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-muted-foreground">
                  {section.stats}
                </div>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {section.title.toLowerCase()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
